"""
URL configuration for backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView
from django_eventstream.views import events
# import django_eventstream.urls

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include('api.urls')),
    path('api/llm/', include('llm_analysis.urls')),
    path('api/auth/', include('auth_api.urls')),
    path('api/knowledge/', include('knowledge_api.urls')),
    path('', RedirectView.as_view(url='/api/', permanent=False)),
    path('sse/match-updates/stream/', events, {'channels': ['match-updates']}, name='sse-match-updates'),
    # path('sse/match-updates/', include(django_eventstream.urls), {
    #         'channels': ['match-updates']
    #     }),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
