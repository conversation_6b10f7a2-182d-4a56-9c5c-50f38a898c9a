<template>
  <div class="card hover:shadow-lg transition-shadow duration-300 p-4">
    <div class="flex justify-between items-center mb-4">
      <div class="text-sm text-gray-600">
        {{ formatDate(match.match_time) }}
      </div>
      <div class="text-sm font-semibold text-primary">
        {{ match.league?.league_short_name || '未知联赛' }}
      </div>
    </div>
    
    <div class="flex justify-between items-center py-2">
      <div class="flex items-center space-x-3 flex-1">
        <div class="team-logo">
          <img :src="match.home_team.team_logo ? `http://localhost:8000${match.home_team.team_logo}` : '/src/assets/default-team-logo.svg'" :alt="match.home_team?.team_name_simp || '未知主队'" @error="handleImageError">
        </div>
        <span class="font-medium">{{ match.home_team?.team_name_simp || '未知主队' }}</span>
      </div>
      
      <div class="flex flex-col items-center mx-4">
        <span class="text-lg font-bold" v-if="match.full_score">
          {{ match.full_score }}
        </span>
        <span class="text-sm text-gray-500" v-if="match.half_score">
          ({{ match.half_score }})
        </span>
        <span v-if="!match.full_score" class="text-sm text-gray-500">
          {{ formatTime(match.match_time) }}
        </span>
      </div>
      
      <div class="flex items-center space-x-3 flex-1 justify-end">
        <span class="font-medium">{{ match.away_team?.team_name_simp || '未知客队' }}</span>
        <div class="team-logo">
          <img :src="match.away_team.team_logo ? `http://localhost:8000${match.away_team.team_logo}` : '/src/assets/default-team-logo.svg'" :alt="match.away_team?.team_name_simp || '未知客队'" @error="handleImageError">
        </div>
      </div>
    </div>

    <div v-if="showOdds && latestOdds" class="mt-4 pt-4 border-t">
      <div class="flex justify-between text-sm">
        <div class="text-center flex-1">
          <div class="font-medium">主胜</div>
          <div class="text-primary">{{ latestOdds.home_win }}</div>
        </div>
        <div class="text-center flex-1">
          <div class="font-medium">平局</div>
          <div class="text-primary">{{ latestOdds.draw }}</div>
        </div>
        <div class="text-center flex-1">
          <div class="font-medium">客胜</div>
          <div class="text-primary">{{ latestOdds.away_win }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { oddsApi } from '../services/api'

interface Odds {
  home_win: number;
  draw: number;
  away_win: number;
}

const props = defineProps<{
  match: any
  showOdds?: boolean
}>()

const latestOdds = ref<Odds | null>(null)

onMounted(async () => {
  if (props.showOdds) {
    try {
      const response = await oddsApi.getLatest(props.match.match_id)
      if (response.data.results.length > 0) {
        latestOdds.value = response.data.results[0]
      }
    } catch (error) {
      console.error('获取赔率失败:', error)
    }
  }
})

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = '/src/assets/default-team-logo.svg';
}

</script>