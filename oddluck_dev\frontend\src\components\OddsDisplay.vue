<template>
  <div class="odds-container" :class="{ 'with-analysis': showAnalysis }">
    <div v-if="loading" class="loading-state">
      <div class="loading-animation">
        <div class="loading-ball loading-ball-1"></div>
        <div class="loading-ball loading-ball-2"></div>
        <div class="loading-ball loading-ball-3"></div>
      </div>
      <h3 class="loading-text">欧赔数据加载中</h3>
      <p class="loading-subtext">正在获取比赛及机构最新数据</p>
    </div>
    <div v-else-if="error" class="error-state">
      <div class="error-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="12"></line>
          <line x1="12" y1="16" x2="12.01" y2="16"></line>
        </svg>
      </div>
      <h3 class="error-message">加载数据出错</h3>
      <p>{{ error }}</p>
      <button class="retry-button" @click="$emit('go-back')">返回首页</button>
    </div>
    <div class="odds" v-else-if="match">
      <div class="match-header">
        <div class="league-info">
          <img 
            v-if="match.league?.logo" 
            :src="`http://localhost:8000${match.league.logo}`" 
            :alt="match.league?.league_short_name || '未知联赛'"
            class="league-logo"
            @error="handleImageError"
          >
          <span class="league-name">{{ match.league?.league_short_name || '未知联赛' }}</span>
        </div>
        
        <!-- 修改赔率分析按钮，增加报告状态提示 -->
        <div class="analysis-actions">
          <button
            class="analysis-button"
            @click="$emit('toggle-analysis')"
            :class="{'has-report': hasExistingReport}"
          >
            <span v-if="hasExistingReport" class="report-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
            </span>
            {{ hasExistingReport ? '查看已有报告' : '赔率分析' }}
          </button>
        </div>
      </div>

      <div class="teams-container">
        <div class="team home">
          <div class="team-logo">
            <img 
              v-if="match.home_team?.team_logo" 
              :src="`http://localhost:8000${match.home_team.team_logo}`" 
              :alt="match.home_team?.team_name_simp || '未知主队'"
              @error="handleImageError"
            >
          </div>
          <div class="team-name">{{ match.home_team?.team_name_simp || '未知主队' }}</div>
        </div>

        <div class="match-center">
          <div class="match-time">{{ formatMatchDateTime(match.match_time) }}</div>
          <div class="score">
            <template v-if="match.full_score">
              {{ match.full_score }}
            </template>
            <template v-else>
              <span class="vs">vs</span>
            </template>
          </div>
        </div>

        <div class="team away">
          <div class="team-logo">
            <img 
              v-if="match.away_team?.team_logo" 
              :src="`http://localhost:8000${match.away_team.team_logo}`" 
              :alt="match.away_team?.team_name_simp || '未知客队'"
              @error="handleImageError"
            >
          </div>
          <div class="team-name">{{ match.away_team?.team_name_simp || '未知客队' }}</div>
        </div>
      </div>

      <div class="odds-table-container" v-if="oddsGroupedByBookmaker">
        <table class="odds-table">
          <thead>
            <tr>
              <th>机构</th>
              <th>主胜</th>
              <th>平局</th>
              <th>客胜</th>
              <th>更新时间</th>
              <th></th>
            </tr>
          </thead>
          <tbody v-if="prepareBookmakerData">
            <template v-for="(bookmaker, name) in prepareBookmakerData" :key="name">
              <!-- 只在有赔率数据时显示该博彩公司 -->
              <template v-if="bookmaker.latest">
                <!-- 最新赔率 -->
                <tr class="latest-odds">
                  <td :rowspan="bookmaker.earliest ? (expandedDrawers[name] && bookmaker.middle.length > 0 ? 3 : 2) : 1">{{ name }}</td>
                  <td :class="getOddsChangeClass(bookmaker.latest.home_win, bookmaker.earliest ? bookmaker.earliest.home_win : null)">
                    {{ bookmaker.latest.home_win.toFixed(2) }}
                  </td>
                  <td :class="getOddsChangeClass(bookmaker.latest.draw, bookmaker.earliest ? bookmaker.earliest.draw : null)">
                    {{ bookmaker.latest.draw.toFixed(2) }}
                  </td>
                  <td :class="getOddsChangeClass(bookmaker.latest.away_win, bookmaker.earliest ? bookmaker.earliest.away_win : null)">
                    {{ bookmaker.latest.away_win.toFixed(2) }}
                  </td>
                  <td>{{ formatMatchTime(bookmaker.latest.update_time) }}</td>
                  <td v-if="bookmaker.middle.length > 0" class="action-cell">
                    <button class="drawer-toggle" @click="toggleDrawer(name)">
                      <span v-if="!expandedDrawers[name]">▼</span>
                      <span v-else>▲</span>
                    </button>
                  </td>
                  <td v-else></td>
                </tr>
                
                <!-- 中间抽屉 -->
                <tr v-if="expandedDrawers[name] && bookmaker.middle.length > 0" class="drawer-container">
                  <td colspan="6" class="drawer-content">
                    <div class="drawer">
                      <table class="inner-table">
                        <thead>
                          <tr>
                            <th>主胜</th>
                            <th>平局</th>
                            <th>客胜</th>
                            <th>更新时间</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(odd, idx) in bookmaker.middle" :key="idx">
                            <td :class="getOddsChangeClass(odd.home_win, 
                              idx < bookmaker.middle.length - 1 
                              ? bookmaker.middle[idx+1].home_win 
                              : null)">
                              {{ odd.home_win.toFixed(2) }}
                            </td>
                            <td :class="getOddsChangeClass(odd.draw, 
                              idx < bookmaker.middle.length - 1 
                              ? bookmaker.middle[idx+1].draw 
                              : null)">
                              {{ odd.draw.toFixed(2) }}
                            </td>
                            <td :class="getOddsChangeClass(odd.away_win, 
                              idx < bookmaker.middle.length - 1 
                              ? bookmaker.middle[idx+1].away_win 
                              : null)">
                              {{ odd.away_win.toFixed(2) }}
                            </td>
                            <td>{{ formatMatchTime(odd.update_time) }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </td>
                </tr>
                
                <!-- 最早赔率 -->
                <tr v-if="bookmaker.earliest" class="earliest-odds">
                  <td :class="getOddsChangeClass(bookmaker.earliest.home_win, null)">
                    {{ bookmaker.earliest.home_win.toFixed(2) }}
                  </td>
                  <td :class="getOddsChangeClass(bookmaker.earliest.draw, null)">
                    {{ bookmaker.earliest.draw.toFixed(2) }}
                  </td>
                  <td :class="getOddsChangeClass(bookmaker.earliest.away_win, null)">
                    {{ bookmaker.earliest.away_win.toFixed(2) }}
                  </td>
                  <td>{{ formatMatchTime(bookmaker.earliest.update_time) }}</td>
                  <td></td>
                </tr>
                
                <!-- 分隔行 -->
                <tr class="separator-row" v-if="name !== Object.keys(prepareBookmakerData).pop()">
                  <td colspan="6"></td>
                </tr>
              </template>
            </template>
          </tbody>
        </table>
      </div>
      <div v-else class="no-odds-message">
        <p>暂无赔率数据</p>
      </div>
    </div>
    <div v-else class="no-data-state">
      <div class="empty-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          <line x1="9" y1="9" x2="15" y2="15"></line>
          <line x1="15" y1="9" x2="9" y2="15"></line>
        </svg>
      </div>
      <h3 class="empty-message">{{ error || `未找到ID为 ${matchId} 的比赛数据` }}</h3>
      <div class="actions">
        <button class="retry-button" @click="goBack">返回首页</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue'
import '../styles/odds-display.css' // 导入外部CSS文件

interface Match {
  id?: string;
  match_id?: string;
  league?: {
    league_id: string;
    league_name: string;
    logo: string;
  };
  match_time: string;
  home_team?: {
    team_id: string;
    team_name: string;
    team_logo: string;
  };
  away_team?: {
    team_id: string;
    team_name: string;
    team_logo: string;
  };
  full_score?: string;
  half_score?: string;
  update_time?: string;
}

interface Odds {
  id: string;
  bookmaker_id: string;
  update_time: string;
  home_win: number;
  draw: number;
  away_win: number;
}

const props = defineProps({
  match: {
    type: Object as () => Match | null,
    default: () => null
  },
  odds: {
    type: Array as () => Odds[],
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: undefined as string | undefined
  },
  analyzing: {
    type: Boolean,
    default: false
  },
  hasExistingReport: {
    type: Boolean,
    default: false
  },
  showAnalysis: {
    type: Boolean,
    default: false
  },
  expandedDrawers: {
    type: Object as () => Record<string, boolean>,
    default: () => ({})
  },
  matchId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits([
  'toggle-analysis', 
  'toggle-drawer', 
  'go-back'
])

// 获取赔率变化样式类
const getOddsChangeClass = (currentOdds: number, previousOdds: number | null) => {
  if (previousOdds === null) return '';
  
  if (currentOdds > previousOdds) {
    return 'odds-increased';
  } else if (currentOdds < previousOdds) {
    return 'odds-decreased';
  }
  
  return '';
};

// 博彩公司映射
const bookmakers: { [key: string]: string } = {
  '281': 'Bet365',
  '82': '立博',
  '115': '威廉希尔',
  // 添加更多公司映射...
}

// 所有支持的博彩公司ID，按照显示顺序排列
const supportedBookmakers = ['115', '82', '281'];

// 按照博彩公司分组赔率数据
const oddsGroupedByBookmaker = computed(() => {
  if (!props.odds.length) return null;
  
  // 使用Map保持指定的排序顺序
  const grouped = new Map<string, Odds[]>();
  
  // 按指定顺序初始化空数组，确保顺序一致
  supportedBookmakers.forEach(id => {
    grouped.set(bookmakers[id], []);
  });
  
  // 将赔率数据分组到对应的博彩公司
  props.odds.forEach(odd => {
    const bookmakerName = getBookmakerName(odd.bookmaker_id);
    if (grouped.has(bookmakerName)) {
      grouped.get(bookmakerName)?.push(odd);
    }
  });
  
  // 确保每个博彩公司的赔率按时间倒序排列
  grouped.forEach((oddsArr) => {
    oddsArr.sort((a, b) => 
      new Date(b.update_time).getTime() - new Date(a.update_time).getTime()
    );
  });
  
  // 转换回对象格式以便在模板中使用
  const result: Record<string, Odds[]> = {};
  grouped.forEach((value, key) => {
    result[key] = value;
  });
  
  return result;
});

// 为每个博彩公司准备要展示的数据
const prepareBookmakerData = computed(() => {
  if (!oddsGroupedByBookmaker.value) return null;

  const result: Record<string, { latest: Odds | null; earliest: Odds | null; middle: Odds[] }> = {};

  Object.entries(oddsGroupedByBookmaker.value).forEach(([name, data]) => {
    if (data.length === 0) {
      result[name] = { latest: null, earliest: null, middle: [] };
    } else if (data.length === 1) {
      // 只有一条记录时，显示为最新的
      result[name] = { latest: data[0], earliest: null, middle: [] };
    } else {
      // 确保数据按时间倒序排列（新→旧）
      const sortedData = [...data].sort((a, b) => 
        new Date(b.update_time).getTime() - new Date(a.update_time).getTime()
      );
      
      // 最新记录是第一条
      const latest = sortedData[0];
      // 最早记录是最后一条
      const earliest = sortedData[sortedData.length - 1];
      
      // 重要变更：保留所有中间数据 - 不再截取到倒数第二条
      // 我们只是移除最新的一条，其余全部作为中间数据
      const middle = sortedData.slice(1);
      
      result[name] = { latest, earliest, middle };
    }
  });

  return result;
});

const formatMatchTime = (dateString: string) => {
  if (!dateString) return '未知';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (e) {
    return dateString; // 如果无法解析日期，返回原始字符串
  }
};

const formatMatchDateTime = (timeString: string | undefined | null) => {
  if (!timeString) return '暂无数据';
  const date = new Date(timeString);
  const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const weekDay = weekDays[date.getDay()];
  return `${timeString.replace('T', ' ').substring(0, 16)} ${weekDay}`;
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/src/assets/default-team-logo.svg'
}

const getBookmakerName = (bookmaker_id: string) => {
  return bookmakers[bookmaker_id] || `公司${bookmaker_id}`
}

const toggleDrawer = (name: string) => {
  emit('toggle-drawer', name);
}

const goBack = () => {
  emit('go-back');
}
</script>

export default {
  // ... existing code ...
} 