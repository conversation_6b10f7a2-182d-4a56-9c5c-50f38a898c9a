<template>
    <div class="match">
      <div class="controls-container">
        <div class="header-controls">
          <div class="date-controls">
            <div class="date-picker-container">
              <button class="date-picker-button" @click="toggleDatePicker">
                <font-awesome-icon icon="calendar" class="calendar-icon" />
                <span class="selected-date">{{ formatDate(selectedDate) }}</span>
              </button>
              <transition name="dropdown">
                <div v-if="showDatePicker" class="date-picker-dropdown">
                  <DatePicker
                    v-model="selectedDate"
                    :min-date="new Date(2020, 0, 1)"
                    :max-date="new Date(2025, 11, 31)"
                    @update:model-value="handleDateChange"
                  />
                </div>
              </transition>
            </div>
            
            <div class="date-tabs">
              <button
                v-for="date in dateRange"
                :key="date.toISOString()"
                :class="['date-tab', { active: isSelectedDate(date) }]"
                @click="selectDate(date)"
              >
                {{ formatDate(date) }}
              </button>
            </div>
          </div>
  
          <div class="view-switcher">
            <div class="filter-box">
              <button class="btn filter-btn" @click.stop="showLeagueFilter = !showLeagueFilter">
                <font-awesome-icon icon="filter" />
                <span>联赛筛选</span>
                <font-awesome-icon 
                  icon="chevron-down" 
                  class="arrow-down"
                  :class="{ 'rotate': showLeagueFilter }"
                />
              </button>
              
              <!-- 联赛筛选弹窗 -->
              <transition name="dropdown">
                <div v-if="showLeagueFilter" class="league-filter-dropdown">
                  <div class="filter-header">
                    <h4>选择联赛</h4>
                    <div class="filter-actions">
                      <button 
                        class="btn btn-sm"
                        @click="selectedLeagues = leagueOptions.map(l => l.id)"
                      >
                        全选
                      </button>
                      <button 
                        class="btn btn-sm"
                        @click="selectedLeagues = []"
                      >
                        清空
                      </button>
                    </div>
                  </div>
                  <div class="league-options">
                    <label 
                      v-for="league in leagueOptions" 
                      :key="league.id"
                      class="league-option"
                    >
                      <input 
                        type="checkbox"
                        v-model="selectedLeagues"
                        :value="league.id"
                      >
                      <img 
                        v-if="league.icon" 
                        :src="league.icon" 
                        :alt="league.name"
                        class="league-icon"
                      >
                      <span>{{ league.name }}</span>
                    </label>
                  </div>
                  <div class="filter-footer">
                    <button 
                      class="btn btn-primary"
                      @click="confirmLeagueFilter"
                    >
                      确定
                    </button>
                  </div>
                </div>
              </transition>
            </div>
            
            <router-link to="/absorb" class="north-ming-button">
              <font-awesome-icon icon="om" class="button-icon" />
              <span>北冥</span>
            </router-link>
          </div>
        </div>
      </div>
  
      <!-- 日历弹窗 -->
      <div v-if="showCalendar" class="calendar-overlay" @click.self="showCalendar = false">
        <div class="calendar-popup">
          <div class="calendar-header">
            <div class="calendar-title">
              <button class="btn btn-text" @click="changeMonth(-1)">
                <font-awesome-icon icon="chevron-left" />
              </button>
              <span>{{ calendarTitle }}</span>
              <button class="btn btn-text" @click="changeMonth(1)">
                <font-awesome-icon icon="chevron-right" />
              </button>
            </div>
          </div>
          <div class="calendar-body">
            <div class="calendar-weekdays">
              <span v-for="day in ['日', '一', '二', '三', '四', '五', '六']" :key="day">{{ day }}</span>
            </div>
            <div class="calendar-days">
              <button
                v-for="{ date, type } in calendarDays"
                :key="date.getTime()"
                class="calendar-day"
                :class="{
                  'other-month': type !== 'current',
                  'today': isToday(date),
                  'selected': isSelectedDate(date)
                }"
                @click="selectDate(date)"
              >
                {{ date.getDate() }}
              </button>
            </div>
          </div>
        </div>
      </div>
  
      <div v-if="error" class="error-message">
        <font-awesome-icon icon="exclamation-circle" />
        <span>{{ error }}</span>
        <button class="btn btn-retry" @click="() => loadMatches()">
          <font-awesome-icon icon="sync" />
          重试
        </button>
      </div>
  
      <div v-if="loadingOverlay" class="loading-overlay">
        <font-awesome-icon icon="spinner" spin class="loading-icon" />
        <span>加载中...</span>
      </div>
  
      <!-- 列表视图 -->
      <div class="match-cards-container">
        <template v-if="groupedMatches.length">
          <div class="league-section" v-for="(matchGroup, index) in groupedMatches" :key="index">
            <div class="league-header">
              <div class="league-icon" v-if="matchGroup.icon">
                <img :src="matchGroup.icon" :alt="matchGroup.name">
              </div>
              <h3 class="league-name">{{ matchGroup.name }}</h3>
            </div>
            
            <div class="match-list">
              <div 
                class="match-item" 
                v-for="match in matchGroup.matches" 
                :key="match.id"
                @click="navigateToOdds(match.id)"
              >
                <!-- 包裹内容以便更好地定位确认按钮 -->
                <div class="match-item-content"> 
                    <div class="match-time">
                        {{ formatTime(match.start) }}
                        <!-- 1. 细微指示器：仅在有未确认更新时显示 -->
                        <span v-if="unacknowledgedUpdates.has(match.id)" 
                              class="update-indicator" 
                              title="赔率已更新，点击右侧按钮确认">
                            <!-- 可以是个小圆点，或者一个 FontAwesome 图标 -->
                            <font-awesome-icon icon="circle" size="xs" /> 
                        </span>
                    </div>
                    <div class="match-teams">
                        <div class="team-container home">
                          <span class="home-team-name">{{ getTeamName(match.homeTeam) }}</span>
                          <div class="team-logo" v-if="match.homeIcon">
                            <img :src="match.homeIcon" :alt="getTeamName(match.homeTeam)" @error="handleImageError">
                          </div>
                        </div>
                        <span class="match-score">
                          <template v-if="match.homeScore !== undefined && match.awayScore !== undefined">
                            {{ match.homeScore }} - {{ match.awayScore }}
                          </template>
                          <template v-else>
                            <span class="vs">vs</span>
                          </template>
                        </span>
                        <div class="team-container away">
                          <div class="team-logo" v-if="match.awayIcon">
                            <img :src="match.awayIcon" :alt="getTeamName(match.awayTeam)" @error="handleImageError">
                          </div>
                          <span class="away-team-name">{{ getTeamName(match.awayTeam) }}</span>
                        </div>
                    </div>
                </div>

                <!-- 2. 手动确认按钮：仅在有未确认更新时显示 -->
                <button v-if="unacknowledgedUpdates.has(match.id)"
                        class="acknowledge-button"
                        @click.stop="acknowledgeUpdate(match.id)" 
                        title="我知道了">
                    <font-awesome-icon icon="check" size="xs"/> 
                </button>
              </div>
            </div>
          </div>
        </template>
        <div v-else class="no-matches">
          <p v-if="matches.length === 0">今天没有赛事</p>
          <p v-else>今天有赛事，请选择联赛</p>
        </div>
      </div>
  
      <!-- 通知提示 -->
      <transition name="notification">
        <div v-if="showNotification" 
             class="notification" 
             :class="notification.type">
          <font-awesome-icon :icon="
            notification.type === 'success' ? 'check' :
            notification.type === 'error' ? 'exclamation-circle' :
            'info-circle'
          " />
          <span>{{ notification.message }}</span>
          <font-awesome-icon 
            icon="times" 
            class="close-icon"
            @click="showNotification = false"
          />
        </div>
      </transition>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
  import axios from 'axios'
  import { library } from '@fortawesome/fontawesome-svg-core'
  import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
  import { format } from 'date-fns'
  import { DatePicker } from 'v-calendar'
  import 'v-calendar/style.css'
  import '../styles/match.css'
  import { 
    faChevronLeft, 
    faChevronRight, 
    faChevronDown,
    faFilter,
    faSpinner,
    faExclamationCircle,
    faSync,
    faTimes,
    faCheck,
    faInfoCircle,
    faCalendarAlt,
    faCalendar,
    faOm,
    faCircle
  } from '@fortawesome/free-solid-svg-icons'
  import { useRouter, useRoute } from 'vue-router'
  
  // 配置axios
  const api = axios.create({
    baseURL: 'http://localhost:8000',
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json'
    }
  })
  
  // --- 新增：赔率更新状态管理 ---
  const UNACKNOWLEDGED_UPDATES_KEY = 'unacknowledgedMatchUpdates'; // Local Storage 键名
  const unacknowledgedUpdates = ref(new Set<string>()); // 存储未确认更新的 matchId
  
  // --- 添加 Watcher 来自动保存状态到 Local Storage ---
  watch(unacknowledgedUpdates, (newSet: Set<string>) => {
      const dataToStore = JSON.stringify(Array.from(newSet));
      console.log('>>> Watcher: Attempting to save to localStorage:', dataToStore); // 日志：尝试保存
      try {
          localStorage.setItem(UNACKNOWLEDGED_UPDATES_KEY, dataToStore);
          console.log('>>> Watcher: Successfully saved to localStorage.'); // 日志：保存成功
      } catch (e) {
          console.error('>>> Watcher: Error saving to localStorage:', e); // 日志：保存错误
      }
  }, { deep: true }); // 使用 deep: true 确保 Set 内部变化也能被监听到

  // 从 Local Storage 加载状态
  const loadUpdatesFromStorage = () => {
    const storedData = localStorage.getItem(UNACKNOWLEDGED_UPDATES_KEY);
    if (storedData) {
      try {
        unacknowledgedUpdates.value = new Set(JSON.parse(storedData));
        console.log('已加载未确认的更新:', unacknowledgedUpdates.value);
      } catch (e) {
        console.error('解析本地存储的更新状态失败:', e);
        localStorage.removeItem(UNACKNOWLEDGED_UPDATES_KEY); // 清理损坏的数据
      }
    }
  };
  
  // 将状态保存到 Local Storage
  const saveUpdatesToStorage = () => {
    try {
      localStorage.setItem(UNACKNOWLEDGED_UPDATES_KEY, JSON.stringify(Array.from(unacknowledgedUpdates.value)));
    } catch (e) {
      console.error('保存更新状态到本地存储失败:', e);
    }
  };
  
  // 处理从 SSE 收到的赔率更新事件
  const handleOddsUpdateSSE = (matchId: string) => {
    if (!unacknowledgedUpdates.value.has(matchId)) { // 只有在之前未标记时才添加
      unacknowledgedUpdates.value.add(matchId);
      saveUpdatesToStorage(); // 添加后立即保存
      console.log(`收到比赛 ${matchId} 的赔率更新，已标记`);
      // 这里可以考虑添加一个短暂的视觉反馈，比如指示器闪烁一下
    } else {
      console.log(`比赛 ${matchId} 已有未确认更新，忽略此次 SSE 事件`);
    }
  };
  
  // 用户点击确认按钮时调用
  const acknowledgeUpdate = (matchId: string) => {
    if (unacknowledgedUpdates.value.has(matchId)) {
      unacknowledgedUpdates.value.delete(matchId);
      saveUpdatesToStorage(); // 确认后更新存储
      console.log(`用户已确认比赛 ${matchId} 的更新`);
    }
  };
  
  
  // --- SSE 监听器设置 ---
  let eventSource: EventSource | null = null;
  const setupSSEListener = () => {
      // --- 使用临时的、简化的 URL 进行测试 ---
      const streamUrl = 'http://localhost:8000/sse/match-updates/stream/'; // Temporary Test URL
      // --- 结束重要提示 ---

      console.log(`尝试连接 SSE: ${streamUrl}`); // 保留这条日志

      // 清理旧的 EventSource (如果存在)
      if (eventSource) {
          console.log('Closing existing SSE connection before creating a new one.');
          eventSource.close();
          eventSource = null;
      }

      try {
          eventSource = new EventSource(streamUrl);

          eventSource.onopen = (event) => {
              console.log('SSE connection opened successfully.', event); // 保留 onopen 日志
          };

          // --- 移除或注释掉通用的 onmessage 处理器 ---
          /*
          eventSource.onmessage = (event) => {
              // 这个处理器不会捕捉到 event: odds_updated 消息
              console.log('>>> Generic SSE message (onmessage):', event);
          };
          */

          // --- 添加针对特定事件类型的监听器 ---
          eventSource.addEventListener('odds_updated', (event) => {
              // 类型断言，因为 addEventListener 的 event 类型比较通用
              const messageEvent = event as MessageEvent;
              console.log('>>> SSE "odds_updated" event received RAW:', messageEvent); // 记录原始事件
              console.log('>>> SSE "odds_updated" data RAW:', messageEvent.data); // 记录原始数据

              try {
                  const data = JSON.parse(messageEvent.data);
                  console.log('Parsed SSE data:', data);

                  if (data && data.match_id) {
                      const route = useRoute();
                      // if (route.name === 'MatchList') {
                          console.log(`Received event for match ${data.match_id}. Specific event type: odds_updated`);

                          const matchIdStr = String(data.match_id);
                          console.log(`Processing odds update for match ${matchIdStr}`);
                          if (!unacknowledgedUpdates.value.has(matchIdStr)) {
                              unacknowledgedUpdates.value.add(matchIdStr);
                              console.log(`Added match ${matchIdStr} to unacknowledged updates. New set:`, unacknowledgedUpdates.value);
                          } else {
                              console.log(`Match ${matchIdStr} already has an unacknowledged update.`);
                          }
                      // } else {
                      //     console.log('Not on MatchList page, ignoring SSE update.');
                      // }
                  } else {
                      console.warn('Received odds_updated event data without match_id or data is null/undefined:', data);
                  }

              } catch (error) {
                  console.error('Error parsing or processing "odds_updated" SSE message data:', error, 'Raw data:', messageEvent.data);
              }
          });

          // 你也可以为其他事件类型添加监听器，例如 keep-alive (如果需要处理)
          eventSource.addEventListener('keep-alive', (event) => {
              console.log('SSE keep-alive received.');
          });

          // 保留 onerror 处理器
          eventSource.onerror = (error) => {
              console.error('SSE connection error:', error);
              if (eventSource) {
                  eventSource.close();
                  console.log('SSE connection closed due to error.');
                  eventSource = null;
              }
          };

      } catch (e) {
          console.error('Failed to create EventSource:', e);
      }
  };
  
  const closeSSEListener = () => {
      if (eventSource) {
          eventSource.close();
          eventSource = null; // 清理引用
          console.log('SSE 连接已关闭');
      }
  };
  // --- 结束 SSE 监听器设置 ---
  
  // 类型声明
  interface Match {
    id: string;
    title: string;
    start: Date;
    end: string;
    url?: string;
    homeTeam: string;
    awayTeam: string;
    homeIcon?: string;
    awayIcon?: string;
    homeScore?: number;
    awayScore?: number;
    leagueId: string;
    leagueName: string;
    leagueIcon?: string;
    odds?: any[];
    showOdds?: boolean;
  }
  
  interface League {
    id: string;
    name: string;
    icon?: string;
    matches: Match[];
  }
  
  interface CalendarDay {
    date: Date;
    type: 'prev' | 'current' | 'next';
  }
  
  interface Notification {
    type: 'success' | 'error' | 'info' | 'warning';
    message: string;
  }
  
  // 注册Font Awesome图标
  library.add(
    faChevronLeft,
    faChevronRight, 
    faChevronDown,
    faFilter,
    faSpinner,
    faExclamationCircle,
    faSync,
    faTimes,
    faCheck,
    faInfoCircle,
    faCalendarAlt,
    faCalendar,
    faOm,
    faCircle
  )
  
  // 视图状态
  const viewTitle = ref('')
  const selectedDate = ref(new Date())
  const showDatePicker = ref(false)
  const showLeagueFilter = ref(false)
  const selectedLeagues = ref<string[]>([])
  const error = ref<string | null>(null)
  const loadingOverlay = ref(false)
  const showNotification = ref(false)
  const notification = ref<Notification>({ type: 'info', message: '' })
  
  // 日期格式化方法
  const formatDate = (date: Date): string => {
    return format(date, 'MM/dd')
  }
  
  const formatTime = (date: Date): string => {
    return format(date, 'HH:mm')
  }
  
  const formatTabDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }
  
  // 日历相关方法
  const isSelectedDate = (date: Date): boolean => {
    return formatTabDate(date) === formatTabDate(selectedDate.value)
  }
  
  const isToday = (date: Date): boolean => {
    return formatTabDate(date) === formatTabDate(new Date())
  }
  
  const handleDateChange = (date: Date) => {
    selectedDate.value = date
    // 更新 URL 参数
    router.replace({
      query: { 
        ...route.query,
        date: formatTabDate(date)
      }
    })
    loadMatches()
  }
  
  // 自动刷新相关
  let autoRefreshInterval: number | null = null
  
  const startAutoRefresh = () => {
    if (autoRefreshInterval) return
    autoRefreshInterval = window.setInterval(() => {
      loadMatches()
    }, 60000) // 每分钟刷新一次
  }
  
  const stopAutoRefresh = () => {
    if (autoRefreshInterval) {
      clearInterval(autoRefreshInterval)
      autoRefreshInterval = null
    }
  }
  
  // 生命周期钩子
  const router = useRouter()
  const route = useRoute()
  
  onMounted(() => {
    // 从 URL 参数中获取日期
    const dateParam = route.query.date as string
    if (dateParam) {
      selectedDate.value = new Date(dateParam)
    }
    
    loadUpdatesFromStorage(); // **新增：加载本地存储的更新状态**
    loadMatches()
    startAutoRefresh()
    setupSSEListener(); // **新增：启动 SSE 监听**
    
    // 修改点击其他区域关闭联赛筛选的逻辑
    document.addEventListener('click', (event) => {
      const filterBox = document.querySelector('.filter-box')
      const target = event.target as Node
      if (filterBox && !filterBox.contains(target)) {
        showLeagueFilter.value = false
      }
    })
  
    // 点击外部关闭日期选择器
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement
      const datePickerContainer = document.querySelector('.date-picker-container')
      if (datePickerContainer && !datePickerContainer.contains(target)) {
        showDatePicker.value = false
      }
    })
  })
  
  onUnmounted(() => {
    stopAutoRefresh()
    closeSSEListener(); // **新增：关闭 SSE 连接**
  })
  
  // 类型定义
  interface Match {
    id: string
    title: string
    start: Date
    end: string
    url?: string
    homeTeam: string
    awayTeam: string
    homeIcon?: string
    awayIcon?: string
    homeScore?: number
    awayScore?: number
    leagueId: string
    leagueName: string
    leagueIcon?: string
    odds?: any[]
    showOdds?: boolean
  }
  
  // 状态
  const matches = ref<Match[]>([])
  
  // 计算属性
  const dateRange = computed(() => {
    const today = new Date()
    const range: Date[] = []
    for (let i = -2; i <= 2; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)
      range.push(date)
    }
    return range
  })
  
  const calendarTitle = computed(() => {
    return format(currentMonth.value, 'yyyy年MM月')
  })
  
  const calendarDays = computed(() => {
    const year = currentMonth.value.getFullYear()
    const month = currentMonth.value.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    
    const days: CalendarDay[] = []
    
    // 上个月的日期
    const prevMonthDays = firstDay.getDay()
    const prevMonth = new Date(year, month, 0)
    for (let i = prevMonthDays - 1; i >= 0; i--) {
      days.push({
        date: new Date(year, month - 1, prevMonth.getDate() - i),
        type: 'prev'
      })
    }
    
    // 当前月的日期
    for (let i = 1; i <= lastDay.getDate(); i++) {
      days.push({
        date: new Date(year, month, i),
        type: 'current'
      })
    }
    
    // 下个月的日期
    const remainingDays = 42 - days.length
    for (let i = 1; i <= remainingDays; i++) {
      days.push({
        date: new Date(year, month + 1, i),
        type: 'next'
      })
    }
    
    return days
  })
  
  const groupedMatches = computed(() => {
    if (!matches.value.length || !selectedLeagues.value.length) return []
    
    const groups = new Map<string, League>()
    
    matches.value.forEach(match => {
      if (selectedLeagues.value.includes(match.leagueId)) {
        if (!groups.has(match.leagueId)) {
          groups.set(match.leagueId, {
            id: match.leagueId,
            name: match.leagueName,
            icon: match.leagueIcon,
            matches: []
          })
        }
        groups.get(match.leagueId)?.matches.push(match)
      }
    })
    
    // 按时间或联赛排序
    const sorted = Array.from(groups.values())
    sorted.forEach(group => {
      group.matches.sort((a, b) => a.start.getTime() - b.start.getTime())
    })
    sorted.sort((a, b) => {
      const aTime = a.matches[0]?.start.getTime() || 0
      const bTime = b.matches[0]?.start.getTime() || 0
      return aTime - bTime
    })
    
    return sorted
  })
  
  const leagueOptions = computed(() => {
    const leagues = new Map<string, { id: string; name: string; icon?: string }>()
    
    matches.value.forEach(match => {
      if (!leagues.has(match.leagueId)) {
        leagues.set(match.leagueId, {
          id: match.leagueId,
          name: match.leagueName,
          icon: match.leagueIcon
        })
      }
    })
    
    return Array.from(leagues.values())
  })
  
  // 方法
  const loadMatches = async () => {
    try {
      loadingOverlay.value = true
      error.value = null
      
      const formattedDate = formatTabDate(selectedDate.value)
      console.log('Fetching matches for date:', formattedDate)
      
      // 检查并记录认证信息
      const token = localStorage.getItem('token')
      console.log('Using token:', token ? 'Yes (token exists)' : 'No (no token)')
      
      const headers = token ? { 'Authorization': `Bearer ${token}` } : {}
      console.log('Request headers:', headers)
      
      // 使用axios实例发送请求
      const response = await api.get(`/api/matches/date/${formattedDate}`, { headers })
      console.log('API Response:', response.status, response.data)
      
      const matchesData = Array.isArray(response.data) ? response.data : response.data.results || []
      console.log('Matches data:', matchesData)
      
      if (matchesData.length === 0) {
        console.log('No matches found for this date')
        matches.value = []
        // 如果没有选择联赛，默认全选
        if (selectedLeagues.value.length === 0) {
            selectedLeagues.value = leagueOptions.value.map(l => l.id);
        }
        return
      }
      
      matches.value = matchesData.map((match: any) => {
        // console.log('Processing match:', match)
        // 确保 home_team 和 away_team 存在
        const homeTeamName = match.home_team?.team_name_simp || '未知主队';
        const awayTeamName = match.away_team?.team_name_simp || '未知客队';
        const homeTeamLogo = match.home_team?.team_logo;
        const awayTeamLogo = match.away_team?.team_logo;
        const leagueId = match.league?.league_id ?? 'unknown';
        const leagueName = match.league?.league_short_name || '未知联赛';
        const leagueLogo = match.league?.logo;
  
        return {
          id: match.match_id,
          title: `${homeTeamName} vs ${awayTeamName}`,
          start: new Date(match.match_time),
          end: match.match_time,
          homeTeam: homeTeamName,
          awayTeam: awayTeamName,
          homeIcon: homeTeamLogo ? `http://localhost:8000${homeTeamLogo}` : '',
          awayIcon: awayTeamLogo ? `http://localhost:8000${awayTeamLogo}` : '',
          homeScore: match.full_score ? parseInt(match.full_score.split('-')[0]) : undefined,
          awayScore: match.full_score ? parseInt(match.full_score.split('-')[1]) : undefined,
          leagueId: leagueId,
          leagueName: leagueName,
          leagueIcon: leagueLogo ? `http://localhost:8000${leagueLogo}` : '',
          odds: match.odds || [],
          showOdds: false
        }
      })
      
      console.log('Processed matches count:', matches.value.length)
      
      // 更新已选择的联赛 - 只有在 selectedLeagues 为空时才自动填充
      if (selectedLeagues.value.length === 0) {
        selectedLeagues.value = [...new Set(matches.value.map(m => m.leagueId))]
        console.log('首次加载或筛选器清空，自动选择所有联赛:', selectedLeagues.value);
      } else {
         console.log('保留用户选择的联赛:', selectedLeagues.value);
      }
      
    } catch (err: any) {
      console.error('Failed to load matches:', {
        error: err.response?.data || err,
        status: err.response?.status,
        url: err.config?.url,
        headers: err.config?.headers
      })
      
      // 更详细的错误信息
      if (err.response?.status === 401) {
        error.value = '加载比赛数据失败：认证令牌无效或已过期，请尝试重新登录'
      } else if (err.response?.status === 404) {
        error.value = '加载比赛数据失败：找不到该日期的比赛数据'
      } else if (err.response?.status === 500) {
        error.value = '加载比赛数据失败：服务器内部错误，请联系管理员'
      } else if (err.code === 'ECONNABORTED') {
        error.value = '加载比赛数据失败：请求超时，请检查网络连接'
      } else {
        error.value = `加载比赛数据失败：${err.message || '未知错误'}`
      }
      
      // 如果有本地缓存数据，尝试使用本地缓存
      // 暂时注释掉缓存逻辑，以避免数据不一致
      // const cachedData = localStorage.getItem(`matches_${formatTabDate(selectedDate.value)}`)
      // if (cachedData) { ... }
    } finally {
      loadingOverlay.value = false
    }
  }
  
  const handleImageError = (event: Event) => {
    const img = event.target as HTMLImageElement
    // 确保使用正确的默认图片路径
    img.src = '/src/assets/default-team-logo.svg' // 或其他正确的静态资源路径
  }
  
  const getTeamName = (name: string): string => {
    return name.length > 10 ? name.slice(0, 10) + '...' : name
  }
  
  // 日历状态
  const showCalendar = ref(false)
  const currentMonth = ref(new Date())
  
  // 日期选择函数
  const selectDate = (date: Date) => {
    selectedDate.value = date
    showCalendar.value = false
    // 更新 URL 参数
    router.replace({
      query: { 
        ...route.query,
        date: formatTabDate(date)
      }
    })
    loadMatches()
  }
  
  // 切换月份
  const changeMonth = (delta: number) => {
    const newDate = new Date(currentMonth.value)
    newDate.setMonth(newDate.getMonth() + delta)
    currentMonth.value = newDate
  }
  
  // 更新视图标题
  const updateViewTitle = (date: string) => {
    viewTitle.value = date
  }
  
  // 切换日期选择器显示状态
  const toggleDatePicker = () => {
    showDatePicker.value = !showDatePicker.value
    if (showDatePicker.value) {
      showLeagueFilter.value = false
    }
  }
  
  // 在script setup部分添加新的方法
  const confirmLeagueFilter = () => {
    showLeagueFilter.value = false
    // 用户点击确定后，根据 selectedLeagues 重新计算 groupedMatches
    // Vue 的计算属性会自动处理
  }
  
  const navigateToOdds = (matchId: string) => {
    console.log('Navigating to odds for match:', matchId)
    
    // 从 matches 数组中找到对应的比赛
    const match = matches.value.find(m => m.id === matchId)
    if (match) {
      console.log('Match details:', match)
      router.push({
        path: `/match/${matchId}`,
        query: {
          // 传递联赛信息，确保在 Odds 页面能获取到正确的比赛数据
          league_id: match.leagueId
        }
      })
    } else {
      console.error('Match not found:', matchId)
      router.push(`/match/${matchId}`) // 即使找不到也尝试跳转，但可能会出错
    }
  }
  </script>